import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LearningPathsService } from './learning-paths.service';
import { LearningPathProgressService } from './learning-path-progress.service';
import { LearningPathIntegrationService } from './learning-path-integration.service';
import { LearningPathEventsService } from './learning-path-events.service';
import { LearningPathRecommendationsService } from './learning-path-recommendations.service';
import { LearningPathsController } from './learning-paths.controller';
import { LearningPath } from '../../entities/learning-path.entity';
import { LearningPathProgress } from '../../entities/learning-path-progress.entity';
import { LearningPathMilestone } from '../../entities/learning-path-milestone.entity';
import { LearningGoal } from '../../entities/learning-goal.entity';
import { User } from '../../entities/user.entity';
import { Course } from '../../entities/course.entity';
import { Assessment } from '../../entities/assessment.entity';
import { ClinicalCase } from '../../entities/clinical-case.entity';
import { Progress } from '../../entities/progress.entity';
import { AssessmentAttempt } from '../../entities/assessment-attempt.entity';
import { ClinicalCaseAttempt } from '../../entities/clinical-case-attempt.entity';
import { CourseEnrollment } from '../../entities/course-enrollment.entity';
import { LearningGoalsModule } from '../learning-goals/learning-goals.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LearningPath,
      LearningPathProgress,
      LearningPathMilestone,
      LearningGoal,
      User,
      Course,
      Assessment,
      ClinicalCase,
      Progress,
      AssessmentAttempt,
      ClinicalCaseAttempt,
      CourseEnrollment,
    ]),
    LearningGoalsModule,
  ],
  controllers: [LearningPathsController],
  providers: [
    LearningPathsService,
    LearningPathProgressService,
    LearningPathIntegrationService,
    LearningPathEventsService,
    LearningPathRecommendationsService,
  ],
  exports: [
    LearningPathsService,
    LearningPathProgressService,
    LearningPathIntegrationService,
    LearningPathRecommendationsService,
  ],
})
export class LearningPathsModule {}
