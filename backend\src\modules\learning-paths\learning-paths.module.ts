import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LearningPathsService } from './learning-paths.service';
import { LearningPathProgressService } from './learning-path-progress.service';
import { LearningPathsController } from './learning-paths.controller';
import { LearningPath } from '../../entities/learning-path.entity';
import { LearningPathProgress } from '../../entities/learning-path-progress.entity';
import { LearningPathMilestone } from '../../entities/learning-path-milestone.entity';
import { User } from '../../entities/user.entity';
import { Course } from '../../entities/course.entity';
import { Assessment } from '../../entities/assessment.entity';
import { ClinicalCase } from '../../entities/clinical-case.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LearningPath,
      LearningPathProgress,
      LearningPathMilestone,
      User,
      Course,
      Assessment,
      ClinicalCase,
    ]),
  ],
  controllers: [LearningPathsController],
  providers: [LearningPathsService, LearningPathProgressService],
  exports: [LearningPathsService, LearningPathProgressService],
})
export class LearningPathsModule {}
